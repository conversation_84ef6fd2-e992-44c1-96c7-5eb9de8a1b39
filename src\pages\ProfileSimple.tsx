import React, { useState, useEffect } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { useThemeStore } from '@/stores/themeStore'
import { useDivinationRecords } from '@/hooks/useDivinationRecords'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { SavedRecordDisplay } from '@/components/SavedRecordDisplay'
import { PaymentButton } from '@/components/PaymentButton'
import { Header } from '@/components/Header'
import { Footer } from '@/components/Footer'
import { DivinationRecord } from '@/lib/supabase'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/components/ui/use-toast'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useTranslation } from '@/hooks/useTranslation'
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>pen,
  Loader2,
  ChevronDown,
  ChevronUp,
  Heart,
  Zap,
  Scroll,
  Coins
} from 'lucide-react'

interface UserCreditsData {
  credits_remaining: number;
  total_purchased: number;
  last_purchase_at: string | null;
}

export const ProfileSimple: React.FC = () => {
  const { user } = useAuthStore()
  const { currentTheme } = useThemeStore()
  const { t } = useTranslation()
  const { records, loading, error } = useDivinationRecords()
  const [expandedRecords, setExpandedRecords] = useState<Set<string>>(new Set())
  const [credits, setCredits] = useState<UserCreditsData | null>(null)
  const [isLoadingCredits, setIsLoadingCredits] = useState(true)
  const { toast } = useToast()

  // 获取用户积分数据
  const fetchCredits = async () => {
    if (!user) {
      setIsLoadingCredits(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('user_credits')
        .select('credits_remaining, total_purchased, last_purchase_at')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        console.error("Error fetching credits:", error);
        throw error;
      }

      setCredits(data || { credits_remaining: 0, total_purchased: 0, last_purchase_at: null });
    } catch (error) {
      console.error("Error fetching credits:", error);
      toast({
        title: t('fetchCreditsError'),
        description: t('cannotFetchAnalysisCount'),
        variant: "destructive",
      });
    } finally {
      setIsLoadingCredits(false);
    }
  };

  useEffect(() => {
    fetchCredits();
  }, [user]);

  // 获取主题特色图标
  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      case 'classic': return Scroll;
      default: return Zap;
    }
  };

  const ThemeIcon = getThemeIcon();




  const toggleRecordExpansion = (recordId: string) => {
    setExpandedRecords(prev => {
      const newSet = new Set(prev)
      if (newSet.has(recordId)) {
        newSet.delete(recordId)
      } else {
        newSet.add(recordId)
      }
      return newSet
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>{t('loading')}</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen">
        <Header />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <p className="text-lg mb-4">{t('pleaseLoginFirst')}</p>
            <Button onClick={() => window.location.href = '/'}>
              {t('backToHome')}
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-lg text-red-600 mb-4">{t('error')}: {error}</p>
          <Button onClick={() => window.location.reload()}>
            {t('reload')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <Header />

      {/* Hero Section for Profile */}
      <section className={`section-spacing ${
        currentTheme === 'cute'
          ? 'bg-gradient-to-br from-pink-50 via-pink-100/30 to-purple-50'
          : 'bg-gradient-to-br from-background via-muted/20 to-background'
      }`}>
        <div className="container-content text-center element-spacing">
          <div className="element-spacing">
            <div className={`w-20 h-20 rounded-full flex items-center justify-center mx-auto ${
              currentTheme === 'cute'
                ? 'bg-gradient-to-br from-pink-400 to-purple-400'
                : 'bg-gradient-to-br from-divination-primary to-divination-accent'
            }`}>
              <User className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold gradient-text leading-tight">
              {currentTheme === 'modern' ? t('dataCenter') :
               currentTheme === 'cute' ? t('myDivinationDiary') :
               t('personalCollection')}
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground">
              {user?.email}
            </p>
          </div>

          <div className="flex flex-wrap justify-center gap-4">
            <Badge variant="secondary" className="px-4 py-2">
              <ThemeIcon className="w-4 h-4 mr-2" />
              {currentTheme === 'modern' ? t('smartStats') :
               currentTheme === 'cute' ? t('warmRecords') :
               t('collectionManagement')}
            </Badge>
            <Badge variant="outline" className="px-4 py-2">
              <Clock className="w-4 h-4 mr-2" />
              {currentTheme === 'modern' ? t('dataAnalysis') :
               currentTheme === 'cute' ? t('timeline') :
               t('historicalReview')}
            </Badge>
          </div>
        </div>
      </section>

      <div className="container-secondary component-spacing">

        {/* 摇卦记录统计 */}
        <div className="grid-responsive-1-2 gap-6 lg:grid-cols-4">
          <Card className={`${
            currentTheme === 'cute' ? 'bg-white/80 border-pink-200' : 'divination-card'
          }`}>
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  currentTheme === 'cute' 
                    ? 'bg-gradient-to-br from-pink-400 to-purple-400' 
                    : 'bg-gradient-to-br from-divination-primary to-divination-accent'
                }`}>
                  <BookOpen className="h-6 w-6 text-white" />
                </div>
                <div>
                  <p className="text-2xl font-bold gradient-text">{records.length}</p>
                  <p className="text-sm text-muted-foreground">{t('totalDivinations')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className={`${
            currentTheme === 'cute' ? 'bg-white/80 border-pink-200' : 'divination-card'
          }`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between space-x-4">
                <div className="flex items-center space-x-4">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    currentTheme === 'cute' 
                      ? 'bg-gradient-to-br from-purple-400 to-pink-400' 
                      : 'bg-gradient-to-br from-divination-secondary to-divination-accent'
                  }`}>
                    <Coins className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold gradient-text">
                      {isLoadingCredits ? '...' : (credits?.credits_remaining || 0)}
                    </p>
                    <p className="text-sm text-muted-foreground">{t('remainingAnalyses')}</p>
                  </div>
                </div>
                
                {/* 购买按钮 */}
                <div className="flex-shrink-0">
                  <PaymentButton onPaymentSuccess={fetchCredits} />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>


        {/* 摇卦记录列表 */}
        <Card className={`${
          currentTheme === 'cute' ? 'bg-white/80 border-pink-200' : 'divination-card'
        }`}>
          <CardHeader className="pb-4">
            <CardTitle className="gradient-text text-2xl font-bold">
              {currentTheme === 'modern' ? t('divinationDataRecords') : 
               currentTheme === 'cute' ? t('myDivinationRecord') : 
               t('hexagramChronicles')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {records.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground">
                <div className="w-20 h-20 bg-background/50 rounded-full flex items-center justify-center mx-auto mb-6">
                  <BookOpen className="h-10 w-10 opacity-50" />
                </div>
                <p className="text-lg mb-2">
                  {currentTheme === 'modern' ? t('noDataRecords') : 
                   currentTheme === 'cute' ? t('noDivinationRecords') : 
                   t('noHexagramChronicles')}
                </p>
                <p className="text-sm">
                  {currentTheme === 'modern' ? t('startFirstAnalysis') : 
                   currentTheme === 'cute' ? t('startFirstDivination') : 
                   t('startIChingJourney')}
                </p>
                <Button
                  variant="mystical"
                  className="mt-4"
                  onClick={() => window.location.href = '/'}
                >
                  {currentTheme === 'modern' ? t('startAnalysis') : 
                   currentTheme === 'cute' ? t('startDivinationHeart') : 
                   t('startHexagram')}
                </Button>
              </div>
            ) : (
              <div className="space-y-6">
                 {records.map((record) => (
                   <div key={record.id} className={`border rounded-lg overflow-hidden ${
                     currentTheme === 'cute' 
                       ? 'border-pink-200 bg-white/60 backdrop-blur-sm' 
                       : 'border-border/50 bg-card/30 backdrop-blur-sm'
                   }`}>
                     {/* 记录头部 - 可点击展开/折叠 */}
                     <div
                       className={`p-4 border-b cursor-pointer transition-colors ${
                         currentTheme === 'cute' 
                           ? 'bg-pink-50/50 border-pink-200 hover:bg-pink-100/50' 
                           : 'bg-background/30 border-border/50 hover:bg-background/50'
                       }`}
                       onClick={() => toggleRecordExpansion(record.id!)}
                     >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary">{record.hexagram_name}</Badge>
                            <Badge variant="outline">第{record.hexagram_number}卦</Badge>
                            {/* 分析状态标识 */}
                            {record.analysis_status === 'pending' && (
                              <Badge variant="outline" className="text-amber-600 border-amber-600">
                                <div className="animate-spin rounded-full h-2 w-2 border-b border-amber-600 mr-1"></div>
                                {t('queueing')}
                              </Badge>
                            )}
                            {record.analysis_status === 'analyzing' && (
                              <Badge variant="outline" className="text-blue-600 border-blue-600">
                                <div className="animate-pulse rounded-full h-2 w-2 bg-blue-600 mr-1"></div>
                                {t('analyzing')}
                              </Badge>
                            )}
                            {record.analysis_status === 'completed' && (
                              <Badge variant="outline" className="text-green-600 border-green-600">
                                ✓ {t('completed')}
                              </Badge>
                            )}
                            {record.analysis_status === 'failed' && (
                              <Badge variant="outline" className="text-red-600 border-red-600">
                                ⚠ {t('failed')}
                              </Badge>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(record.divination_time), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
                          </div>
                          {record.question && (
                            <div className="text-sm text-foreground max-w-md truncate">
                              {t('question')}: {record.question}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          {/* 展开/折叠图标 */}
                          {expandedRecords.has(record.id!) ? (
                            <ChevronUp className="h-5 w-5 text-muted-foreground" />
                          ) : (
                            <ChevronDown className="h-5 w-5 text-muted-foreground" />
                          )}
                        </div>
                      </div>
                    </div>
                    
                     {/* 记录详细内容 - 展开时显示 */}
                     {expandedRecords.has(record.id!) && (
                       <div className={`p-6 ${
                         currentTheme === 'cute' 
                           ? 'bg-pink-50/30' 
                           : 'bg-background/20'
                       }`}>
                         <SavedRecordDisplay record={record} />
                       </div>
                     )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  )
}
