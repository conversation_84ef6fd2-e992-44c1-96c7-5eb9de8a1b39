import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { useThemeStore } from "@/stores/themeStore";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Home, Search, ArrowLeft, Compass } from "lucide-react";
import Header from "@/components/Header";

const NotFound = () => {
  const location = useLocation();
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  const getThemeStyles = () => {
    switch (currentTheme) {
      case 'cute':
        return {
          background: 'bg-gradient-to-br from-pink-50 via-pink-100/30 to-purple-50',
          iconBg: 'bg-gradient-to-br from-pink-400 to-purple-400',
          textColor: 'text-pink-600'
        };
      case 'classic':
        return {
          background: 'bg-gradient-to-br from-amber-50 via-orange-50/30 to-red-50',
          iconBg: 'bg-gradient-to-br from-amber-600 to-red-600',
          textColor: 'text-amber-700'
        };
      default:
        return {
          background: 'bg-gradient-to-br from-background via-muted/20 to-background',
          iconBg: 'bg-gradient-to-br from-divination-primary to-divination-accent',
          textColor: 'text-foreground'
        };
    }
  };

  const themeStyles = getThemeStyles();

  return (
    <div className="min-h-screen">
      <Header />

      <section className={`section-spacing ${themeStyles.background}`}>
        <div className="container-content text-center component-spacing">
          <div className="element-spacing">
            <div className={`w-24 h-24 rounded-full flex items-center justify-center mx-auto ${themeStyles.iconBg}`}>
              <Compass className="h-12 w-12 text-white" />
            </div>

            <div className="element-spacing">
              <h1 className="text-8xl md:text-9xl font-bold gradient-text leading-tight">
                404
              </h1>
              <h2 className={`text-3xl md:text-4xl font-bold mb-4 ${themeStyles.textColor}`}>
                {currentTheme === 'modern' ? '页面未找到' :
                 currentTheme === 'cute' ? '哎呀，迷路了呢～' :
                 '此路不通'}
              </h2>
              <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
                {currentTheme === 'modern' ? '您访问的页面不存在，可能已被移动或删除。' :
                 currentTheme === 'cute' ? '小可爱，这个页面好像不见了呢～让我们一起回到温暖的首页吧！' :
                 '所寻之页已不在此处，或许应当重新寻找正确的道路。'}
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-4">
              <Badge variant="secondary" className="px-4 py-2">
                <Search className="w-4 h-4 mr-2" />
                {location.pathname}
              </Badge>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              variant="default"
              size="lg"
              className="text-lg px-8 py-6"
            >
              <Link to="/">
                <Home className="w-5 h-5 mr-2" />
                {currentTheme === 'modern' ? '返回首页' :
                 currentTheme === 'cute' ? '回到首页' :
                 '回归首页'}
              </Link>
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              {currentTheme === 'modern' ? '返回上页' :
               currentTheme === 'cute' ? '回到上一页' :
               '退回前页'}
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NotFound;
